//go:build integration

// Integration tests for signal correlation functionality
// Tests correlation ID validation, cross-account correlations, and ISN boundary enforcement
package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
)

// createCorrelatedSignalPayload creates a signal payload with a correlation_id
func createCorrelatedSignalPayload(localRef string, correlationID uuid.UUID) map[string]any {
	return map[string]any{
		"signals": []map[string]any{
			{
				"local_ref":      localRef,
				"correlation_id": correlationID,
				"content": map[string]any{
					"test": "valid content for simple schema",
				},
			},
		},
	}
}

// submitCorrelatedSignal submits a signal with correlation ID and returns the response
func submitCorrelatedSignal(t *testing.T, baseURL string, localRef string, correlationID uuid.UUID, token string, endpoint testSignalEndpoint) *http.Response {
	t.Helper()

	payload := createCorrelatedSignalPayload(localRef, correlationID)
	return submitSignalRequest(t, baseURL, payload, token, endpoint)
}

// getSignalIDFromSearch searches for a signal and returns its signal_id
func getSignalIDFromSearch(t *testing.T, baseURL string, endpoint testSignalEndpoint, token string, localRef string) uuid.UUID {
	t.Helper()

	response := searchPrivateSignals(t, baseURL, endpoint, token, false)
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		t.Fatalf("Failed to search for signal %s: %d", localRef, response.StatusCode)
	}

	var signals []map[string]any
	if err := json.NewDecoder(response.Body).Decode(&signals); err != nil {
		t.Fatalf("Failed to decode search response: %v", err)
	}

	for _, signal := range signals {
		if signal["local_ref"] == localRef {
			signalIDStr, ok := signal["signal_id"].(string)
			if !ok {
				t.Fatalf("Signal ID is not a string: %v", signal["signal_id"])
			}
			signalID, err := uuid.Parse(signalIDStr)
			if err != nil {
				t.Fatalf("Failed to parse signal ID: %v", err)
			}
			return signalID
		}
	}

	t.Fatalf("Signal with local_ref %s not found in search results", localRef)
	return uuid.Nil
}

// TestCorrelationIDs tests the complete correlation ID functionality
func TestCorrelationIDs(t *testing.T) {
	ctx := context.Background()
	testDB := setupTestDatabase(t, ctx)
	testEnv := setupTestEnvironment(testDB)

	t.Log("Creating test data...")

	// Create accounts
	adminAccount := createTestAccount(t, ctx, testEnv.queries, "admin", "user", "<EMAIL>")
	ownerAccount := createTestAccount(t, ctx, testEnv.queries, "owner", "user", "<EMAIL>")
	memberAccount := createTestAccount(t, ctx, testEnv.queries, "member", "user", "<EMAIL>")

	// Create ISNs
	adminISN := createTestISN(t, ctx, testEnv.queries, "admin-correlation-isn", "Admin Correlation ISN", adminAccount.ID, "private")
	ownerISN := createTestISN(t, ctx, testEnv.queries, "owner-correlation-isn", "Owner Correlation ISN", ownerAccount.ID, "private")

	// Create signal types
	adminSignalType, err := testEnv.queries.CreateSignalType(ctx, database.CreateSignalTypeParams{
		IsnID:         adminISN.ID,
		Slug:          "correlation-signal",
		SchemaURL:     testSchemaURL,
		ReadmeURL:     testReadmeURL,
		Title:         "Correlation Test Signal",
		Detail:        "Signal type for correlation testing",
		SemVer:        "1.0.0",
		SchemaContent: testSchemaContent,
	})
	if err != nil {
		t.Fatalf("Failed to create admin signal type: %v", err)
	}

	ownerSignalType, err := testEnv.queries.CreateSignalType(ctx, database.CreateSignalTypeParams{
		IsnID:         ownerISN.ID,
		Slug:          "owner-correlation-signal",
		SchemaURL:     testSchemaURL,
		ReadmeURL:     testReadmeURL,
		Title:         "Owner Correlation Test Signal",
		Detail:        "Signal type for owner correlation testing",
		SemVer:        "1.0.0",
		SchemaContent: testSchemaContent,
	})
	if err != nil {
		t.Fatalf("Failed to create owner signal type: %v", err)
	}

	// Grant permissions
	_, err = testEnv.queries.CreateIsnAccount(ctx, database.CreateIsnAccountParams{
		IsnID:      adminISN.ID,
		AccountID:  ownerAccount.ID,
		Permission: "write",
	})
	if err != nil {
		t.Fatalf("Failed to grant owner permission to admin ISN: %v", err)
	}

	_, err = testEnv.queries.CreateIsnAccount(ctx, database.CreateIsnAccountParams{
		IsnID:      adminISN.ID,
		AccountID:  memberAccount.ID,
		Permission: "read",
	})
	if err != nil {
		t.Fatalf("Failed to grant member permission to admin ISN: %v", err)
	}

	// Create access tokens
	adminToken := testEnv.createAuthToken(t, adminAccount.ID)
	ownerToken := testEnv.createAuthToken(t, ownerAccount.ID)

	// Start HTTP server after creating test data
	testURL := getTestDatabaseURL()
	baseURL, stopServer := startInProcessServer(t, ctx, testEnv.dbConn, testURL)
	defer stopServer()

	// Define endpoints
	adminEndpoint := testSignalEndpoint{
		isnSlug:          adminISN.Slug,
		signalTypeSlug:   adminSignalType.Slug,
		signalTypeSemVer: adminSignalType.SemVer,
	}

	ownerEndpoint := testSignalEndpoint{
		isnSlug:          ownerISN.Slug,
		signalTypeSlug:   ownerSignalType.Slug,
		signalTypeSemVer: ownerSignalType.SemVer,
	}

	// Run correlation tests
	t.Run("valid correlation within same ISN", func(t *testing.T) {
		// Step 1: Admin creates a base signal
		basePayload := createValidSignalPayload("admin-base-signal")
		response := submitSignalRequest(t, baseURL, basePayload, adminToken, adminEndpoint)
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			logResponseDetails(t, response, "admin base signal submission")
			t.Fatalf("Failed to submit base signal: %d", response.StatusCode)
		}

		// Step 1.5: Verify we can search and find the base signal
		searchResponse := searchPrivateSignals(t, baseURL, adminEndpoint, adminToken, false)
		defer searchResponse.Body.Close()

		if searchResponse.StatusCode != http.StatusOK {
			t.Fatalf("Failed to search for base signal: %d", searchResponse.StatusCode)
		}

		var signals []map[string]any
		if err := json.NewDecoder(searchResponse.Body).Decode(&signals); err != nil {
			t.Fatalf("Failed to decode search response: %v", err)
		}

		t.Logf("Found %d signals in search", len(signals))
		for i, signal := range signals {
			t.Logf("Signal %d: local_ref=%v, signal_id=%v", i, signal["local_ref"], signal["signal_id"])
		}

		// Get the signal ID of the base signal
		baseSignalID := getSignalIDFromSearch(t, baseURL, adminEndpoint, adminToken, "admin-base-signal")

		// Step 2: Owner creates a correlated signal (cross-account correlation within same ISN)
		correlatedResponse := submitCorrelatedSignal(t, baseURL, "owner-correlated-signal", baseSignalID, ownerToken, adminEndpoint)
		defer correlatedResponse.Body.Close()

		if correlatedResponse.StatusCode != http.StatusOK {
			logResponseDetails(t, correlatedResponse, "owner correlated signal submission")
			t.Fatalf("Failed to submit correlated signal: %d", correlatedResponse.StatusCode)
		}

		// Verify the response indicates success
		var correlatedResult map[string]any
		if err := json.NewDecoder(correlatedResponse.Body).Decode(&correlatedResult); err != nil {
			t.Fatalf("Failed to decode correlated signal response: %v", err)
		}

		results, ok := correlatedResult["results"].(map[string]any)
		if !ok {
			t.Fatalf("Response missing results field: %v", correlatedResult)
		}

		storedCount, ok := results["stored_count"].(float64)
		if !ok || storedCount != 1 {
			t.Errorf("Expected stored_count=1, got %v", results["stored_count"])
		}

		failedCount, ok := results["failed_count"].(float64)
		if !ok || failedCount != 0 {
			t.Errorf("Expected failed_count=0, got %v", results["failed_count"])
		}

		// Step 3: Verify correlation appears in search results
		searchResponse2 := searchPrivateSignals(t, baseURL, adminEndpoint, adminToken, false)
		defer searchResponse2.Body.Close()

		if searchResponse2.StatusCode != http.StatusOK {
			t.Fatalf("Failed to search signals: %d", searchResponse2.StatusCode)
		}

		var allSignals []map[string]any
		if err := json.NewDecoder(searchResponse2.Body).Decode(&allSignals); err != nil {
			t.Fatalf("Failed to decode search response: %v", err)
		}

		// Find the correlated signal and verify correlation fields
		var correlatedSignal map[string]any
		for _, signal := range allSignals {
			if signal["local_ref"] == "owner-correlated-signal" {
				correlatedSignal = signal
				break
			}
		}

		if correlatedSignal == nil {
			t.Fatal("Correlated signal not found in search results")
		}

		// Verify correlation fields
		if correlatedSignal["correlated_local_ref"] != "admin-base-signal" {
			t.Errorf("Expected correlated_local_ref='admin-base-signal', got %v", correlatedSignal["correlated_local_ref"])
		}

		correlatedSignalIDStr, ok := correlatedSignal["correlated_signal_id"].(string)
		if !ok {
			t.Fatalf("correlated_signal_id is not a string: %v", correlatedSignal["correlated_signal_id"])
		}

		correlatedSignalID, err := uuid.Parse(correlatedSignalIDStr)
		if err != nil {
			t.Fatalf("Failed to parse correlated_signal_id: %v", err)
		}

		if correlatedSignalID != baseSignalID {
			t.Errorf("Expected correlated_signal_id=%v, got %v", baseSignalID, correlatedSignalID)
		}
	})

	t.Run("invalid correlation across ISNs", func(t *testing.T) {
		// Step 1: Create a signal in the owner's ISN
		ownerPayload := createValidSignalPayload("owner-isn-signal")
		response := submitSignalRequest(t, baseURL, ownerPayload, ownerToken, ownerEndpoint)
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			logResponseDetails(t, response, "owner ISN signal submission")
			t.Fatalf("Failed to submit signal to owner ISN: %d", response.StatusCode)
		}

		// Get the signal ID from the owner's ISN
		ownerSignalID := getSignalIDFromSearch(t, baseURL, ownerEndpoint, ownerToken, "owner-isn-signal")

		// Step 2: Try to correlate a signal in admin ISN to the signal in owner ISN (should fail)
		crossIsnResponse := submitCorrelatedSignal(t, baseURL, "admin-cross-isn-signal", ownerSignalID, adminToken, adminEndpoint)
		defer crossIsnResponse.Body.Close()

		// Should return 200 but with failed signals in the response
		if crossIsnResponse.StatusCode != http.StatusOK {
			logResponseDetails(t, crossIsnResponse, "cross-ISN correlation attempt")
			t.Fatalf("Expected 200 status for cross-ISN correlation attempt, got: %d", crossIsnResponse.StatusCode)
		}

		// Verify the response indicates failure
		var result map[string]any
		if err := json.NewDecoder(crossIsnResponse.Body).Decode(&result); err != nil {
			t.Fatalf("Failed to decode cross-ISN correlation response: %v", err)
		}

		results, ok := result["results"].(map[string]any)
		if !ok {
			t.Fatalf("Response missing results field: %v", result)
		}

		storedCount, ok := results["stored_count"].(float64)
		if !ok || storedCount != 0 {
			t.Errorf("Expected stored_count=0 for cross-ISN correlation, got %v", results["stored_count"])
		}

		failedCount, ok := results["failed_count"].(float64)
		if !ok || failedCount != 1 {
			t.Errorf("Expected failed_count=1 for cross-ISN correlation, got %v", results["failed_count"])
		}

		// Verify the error message
		failedSignals, ok := results["failed_signals"].([]any)
		if !ok || len(failedSignals) != 1 {
			t.Fatalf("Expected 1 failed signal, got %v", results["failed_signals"])
		}

		failedSignal, ok := failedSignals[0].(map[string]any)
		if !ok {
			t.Fatalf("Failed signal is not a map: %v", failedSignals[0])
		}

		if failedSignal["local_ref"] != "admin-cross-isn-signal" {
			t.Errorf("Expected failed signal local_ref='admin-cross-isn-signal', got %v", failedSignal["local_ref"])
		}

		errorMessage, ok := failedSignal["error_message"].(string)
		if !ok {
			t.Fatalf("Error message is not a string: %v", failedSignal["error_message"])
		}

		expectedErrorPattern := fmt.Sprintf("invalid correlation_id %v - signal does not exist in this ISN", ownerSignalID)
		if errorMessage != expectedErrorPattern {
			t.Errorf("Expected error message '%s', got '%s'", expectedErrorPattern, errorMessage)
		}
	})

	t.Run("correlation ID validation", func(t *testing.T) {
		// Test with a completely non-existent correlation ID
		nonExistentID := uuid.New() // Generate a random UUID that doesn't exist

		invalidResponse := submitCorrelatedSignal(t, baseURL, "admin-invalid-correlation", nonExistentID, adminToken, adminEndpoint)
		defer invalidResponse.Body.Close()

		// Should return 200 but with failed signals in the response
		if invalidResponse.StatusCode != http.StatusOK {
			logResponseDetails(t, invalidResponse, "invalid correlation ID attempt")
			t.Fatalf("Expected 200 status for invalid correlation ID attempt, got: %d", invalidResponse.StatusCode)
		}

		// Verify the response indicates failure
		var result map[string]any
		if err := json.NewDecoder(invalidResponse.Body).Decode(&result); err != nil {
			t.Fatalf("Failed to decode invalid correlation response: %v", err)
		}

		results, ok := result["results"].(map[string]any)
		if !ok {
			t.Fatalf("Response missing results field: %v", result)
		}

		storedCount, ok := results["stored_count"].(float64)
		if !ok || storedCount != 0 {
			t.Errorf("Expected stored_count=0 for invalid correlation ID, got %v", results["stored_count"])
		}

		failedCount, ok := results["failed_count"].(float64)
		if !ok || failedCount != 1 {
			t.Errorf("Expected failed_count=1 for invalid correlation ID, got %v", results["failed_count"])
		}

		// Verify the error message
		failedSignals, ok := results["failed_signals"].([]any)
		if !ok || len(failedSignals) != 1 {
			t.Fatalf("Expected 1 failed signal, got %v", results["failed_signals"])
		}

		failedSignal, ok := failedSignals[0].(map[string]any)
		if !ok {
			t.Fatalf("Failed signal is not a map: %v", failedSignals[0])
		}

		if failedSignal["local_ref"] != "admin-invalid-correlation" {
			t.Errorf("Expected failed signal local_ref='admin-invalid-correlation', got %v", failedSignal["local_ref"])
		}

		errorMessage, ok := failedSignal["error_message"].(string)
		if !ok {
			t.Fatalf("Error message is not a string: %v", failedSignal["error_message"])
		}

		expectedErrorPattern := fmt.Sprintf("invalid correlation_id %v - signal does not exist in this ISN", nonExistentID)
		if errorMessage != expectedErrorPattern {
			t.Errorf("Expected error message '%s', got '%s'", expectedErrorPattern, errorMessage)
		}

		errorCode, ok := failedSignal["error_code"].(string)
		if !ok {
			t.Fatalf("Error code is not a string: %v", failedSignal["error_code"])
		}

		if errorCode != "malformed_body" {
			t.Errorf("Expected error_code='malformed_body', got '%s'", errorCode)
		}
	})

	t.Run("correlation updates", func(t *testing.T) {
		// TODO: Implement test for correlation ID updates
		t.Skip("Test implementation pending")
	})

	t.Run("correlation visibility in search", func(t *testing.T) {
		// TODO: Implement test for correlation visibility
		t.Skip("Test implementation pending")
	})

	t.Run("correlation with withdrawn signals", func(t *testing.T) {
		// TODO: Implement test for correlation with withdrawn signals
		t.Skip("Test implementation pending")
	})
}
